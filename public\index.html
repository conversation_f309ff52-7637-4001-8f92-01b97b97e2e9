<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Lucky Bhai Ka</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Automate Form</h1>
        <div class="status success">
            <h2>🚀 API Server is Running!</h2>
            <p>Welcome to the API Automation Platform</p>
            <p>Server is active and ready to handle requests.</p>
        </div>

        <div style="margin-top: 30px;">
            <h3>Available Endpoints:</h3>
            <ul>
                <li><strong>GET /</strong> - This homepage</li>
                <li><strong>POST /chat</strong> - AI Chat Support (Kritika)</li>
                <li><strong>POST /v1/users</strong> - User management endpoints</li>
                <li><strong>GET /public/*</strong> - Static file serving</li>
            </ul>
        </div>

        <div style="margin-top: 30px; border: 1px solid #ddd; padding: 20px; border-radius: 8px;">
            <h3>🤖 Chat with Kritika (AI Support)</h3>
            <div id="chatMessages" style="height: 200px; overflow-y: auto; border: 1px solid #eee; padding: 10px; margin: 10px 0; background: #f9f9f9;"></div>
            <div style="display: flex; gap: 10px;">
                <input type="text" id="messageInput" placeholder="Type your message..." style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                <button onclick="sendMessage()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Send</button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>API Version: 1.0.0</p>
            <p>Status: Active ✅</p>
        </div>
    </div>

    <script>
        let conversationUuid = null;

        function addMessage(message, isUser = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 8px;
                ${isUser ?
                    'background: #007bff; color: white; text-align: right; margin-left: 20%;' :
                    'background: #e9ecef; color: #333; margin-right: 20%;'
                }
            `;
            messageDiv.innerHTML = `<strong>${isUser ? 'You' : 'Kritika'}:</strong> ${message}`;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessage(message, true);
            messageInput.value = '';

            // Show typing indicator
            addMessage('Typing...', false);

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        uuid: conversationUuid,
                        message: message
                    })
                });

                const data = await response.json();

                // Remove typing indicator
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.removeChild(chatMessages.lastChild);

                if (response.ok) {
                    conversationUuid = data.uuid;
                    addMessage(data.answer, false);
                } else {
                    addMessage('Error: ' + (data.error || 'Something went wrong'), false);
                }
            } catch (error) {
                // Remove typing indicator
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.removeChild(chatMessages.lastChild);
                addMessage('Error: Failed to send message', false);
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Add welcome message
        addMessage('Hi! I\'m Kritika, your AI support assistant. How can I help you today?', false);
    </script>
</body>
</html>