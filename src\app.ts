
import { Hono } from "hono";
import { cors } from "hono/cors";
import { serveStatic } from 'hono/bun'
import { user } from "./routes/user.routes";
import { chat } from "./routes/chat.routes";
import { readFileSync } from "fs";
import { join } from "path";

//server
const app = new Hono();
//cors handling
const envOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(",")
  : [];
  const allowedOrigins = new Set(envOrigins);
app.use(
  cors({
    origin: (origin) => {
      if (!origin) return "http://localhost:3000";
      return allowedOrigins.has(origin) ? origin : null;
    },
    credentials: true,
  })
);
//static files
app.use("/public/*", serveStatic({ root: "./public" }));

//request size limit
app.use("*", async (c, next) => {
  const contentType = c.req.header("Content-Type") || "";
  console.log(`📢 Incoming Request: ${c.req.method} ${c.req.url}`);
  if (
    contentType.includes("application/json") ||
    contentType.includes("application/x-www-form-urlencoded")
  ) {
    const contentLength = c.req.header("Content-Length");
    if (contentLength && parseInt(contentLength) > 5 * 1024 * 1024) {
      return c.text("Payload Too Large", 413);
    }
  }
  await next();
});

//routes
app.route("/v1/users", user);
app.route("/", chat);
//default route
app.get("/", (c) => {
  try {
    const htmlContent = readFileSync(join(process.cwd(), "public", "index.html"), "utf-8");
    return c.html(htmlContent);
  } catch (error) {
    return c.text("HTML file not found", 404);
  }
});
//404
app.notFound((c) => {
  return c.text("Api enpoint not found", 404);
});

export {app}
