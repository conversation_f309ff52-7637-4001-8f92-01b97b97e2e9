import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";


const registerUser = async (c: Context) => {
  try {
    const body: any = await c.req.json();
    const { firstName, lastName, email, password, confirmPassword, phone, country, countryCode, terms_conditions } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password || !confirmPassword || !phone || !country || !countryCode || !terms_conditions) {
      return sendApiError(c, "All fields are required!");
    }
    if (!terms_conditions) {
      return sendApiError(c, "You must accept the terms and conditions!");
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      return sendApiError(c, "Passwords do not match!");
    }

    // Check if the user already exists
    const { data: existingUser, error: existingUserError } = await supabase
      .from("user_profile")
      .select("id")
      .eq("email", email)
      .single();

    if (existingUser) {
      return sendApiError(c, "User already registered!");
    }

    // Register user using Supabase auth
    const { data, error } = await supabase.auth.signUp({ email, password });
    if (error) {
      return sendApiError(c, error.message);
    }

    // Clean and validate phone number
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return sendApiError(c, "Invalid phone number.");
    }

    // const numericPhone = BigInt(cleanPhone);

    // Insert user profile
    const { data: userData, error: profileError } = await supabase
      .from("user_profile")
      .upsert([
        {
          id: data?.user?.id,
          first_name: firstName,
          last_name: lastName,
          phone: cleanPhone,
          email,
          country,
          country_code: countryCode,
          terms_conditions
        },
      ])
      .select()
      .single();

    if (profileError) {
      return sendApiError(c, profileError.message);
    }

    // Return success response
    return sendApiResponse(c, {
      message: "Registration successful! Please check your email to verify your account.",
      user: {
        id: userData?.id,
        email: userData?.email,
        firstName: userData?.first_name,
        lastName: userData?.last_name,
      },
    });
  } catch (err) {
    console.error("Registration Error:", err);
    return sendApiError(c, "Internal server error");
  }
};

export {  registerUser};
