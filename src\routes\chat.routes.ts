import { Hono } from "hono";
import { createClient } from '@supabase/supabase-js';

const chat = new Hono();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const openaiApiKey = process.env.OPENAI_API_KEY;

const supabase = createClient(supabaseUrl!, supabaseKey!);
const match_threshold = 0.8;

async function getEmbedding(text: string) {
  const res = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${openaiApiKey}`,
    },
    body: JSON.stringify({
      input: text,
      model: 'text-embedding-ada-002',
    }),
  });
  if (!res.ok) {
    const err = await res.text();
    throw new Error(`OpenAI API error: ${err}`);
  }
  const data = await res.json();
  return data.data[0].embedding;
}

function getRecentHistory(conversationArray: any[], n = 6) {
  return conversationArray
    .slice(-n)
    .map(
      (msg) =>
        `${msg.role === 'user' ? 'User' : 'AI'}: ${msg.content}`
    )
    .join('\n');
}

async function getChatCompletion(messages: any[]) {
  const res = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${openaiApiKey}`,
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: messages,
      temperature: 0.2,
      max_tokens: 512,
    }),
  });
  
  if (!res.ok) {
    const err = await res.text();
    throw new Error(`OpenAI API error: ${err}`);
  }
  
  const data = await res.json();
  return data.choices[0].message.content;
}

chat.post('/chat', async (c) => {
  try {
    const { uuid, message } = await c.req.json();

    if (!message) {
      return c.json({ error: 'Missing message' }, 400);
    }

    let conversationId = uuid;
    let conversationArray: any[] = [];

    // Load or create conversation
    if (conversationId) {
      const { data, error } = await supabase
        .from('ai_support_conversation_history')
        .select('id, conversation, chat_name')
        .eq('id', conversationId)
        .single();
      
      if (error || !data) {
        return c.json({
          error: error?.message || 'Conversation not found for UUID: ' + uuid,
        }, 404);
      }
      conversationArray = data.conversation || [];
    } else {
      const first10Words = message
        .split(/\s+/)
        .slice(0, 10)
        .join(' ');

      const { data, error } = await supabase
        .from('ai_support_conversation_history')
        .insert([
          {
            conversation: [],
            chat_name: first10Words,
          },
        ])
        .select('id, conversation, chat_name')
        .single();
      
      if (error) {
        return c.json({ error: error.message }, 500);
      }
      conversationId = data.id;
      conversationArray = data.conversation || [];
    }

    // Add user message
    conversationArray.push({ role: 'user', content: message });

    // Get embedding
    let embedding;
    try {
      embedding = await getEmbedding(message);
    } catch (err: any) {
      return c.json({ error: 'Embedding error: ' + err.message }, 500);
    }

    // Get matching docs
    let documents, matchError;
    try {
      const result = await supabase.rpc('match_support_document_sections', {
        p_embedding: embedding,
        p_match_threshold: match_threshold,
      });
      documents = result.data;
      matchError = result.error;
    } catch (err) {
      matchError = err;
    }
    
    if (matchError) {
      return c.json({
        error: 'Supabase error: ' + (matchError.message || matchError),
      }, 500);
    }

    // Inject docs and recent conversation
    const injectedDocs =
      documents && documents.length > 0
        ? documents.map((doc: any) => doc.content).join('\n\n')
        : 'No documents found';

    const recentHistory = getRecentHistory(conversationArray, 6);

    // Build system prompt
    const systemPrompt = `
you are a professional and friendly support agent for Automate Business and your name is kritika reply just like a human being.

Your job is to help users by answering their questions using only the information provided in the "Documents" and the recent "Conversation History" below.

Guidelines:
- Always be polite, concise, and clear.
- If the user's question refers to something or someone mentioned earlier in the conversation, use that information from the conversation history.
- If you need more information to answer accurately, politely ask the user for clarification.
- If the answer is not found in the provided documents or conversation history, respond with:
  "Sorry, I couldn't find any information on that."
- Do not speculate, make up answers, or go off topic.
- If the user asks about something unrelated to the documents or conversation, gently redirect them to relevant topics.

Recent Conversation:
${recentHistory}

Documents:
${injectedDocs}
`;

    // Prepare messages for OpenAI
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: message }
    ];

    // Get AI response
    let aiResponse;
    try {
      aiResponse = await getChatCompletion(messages);
    } catch (err: any) {
      return c.json({ error: 'OpenAI error: ' + err.message }, 500);
    }

    // Add AI message
    conversationArray.push({ role: 'ai', content: aiResponse });

    // Save conversation
    const { error: updateError } = await supabase
      .from('ai_support_conversation_history')
      .update({ conversation: conversationArray })
      .eq('id', conversationId);

    if (updateError) {
      return c.json({
        error: 'Failed to update conversation history: ' + updateError.message,
      }, 500);
    }

    // Return response
    return c.json({
      uuid: conversationId,
      answer: aiResponse,
    });

  } catch (error: any) {
    return c.json({ error: 'Internal server error: ' + error.message }, 500);
  }
});

export { chat };
